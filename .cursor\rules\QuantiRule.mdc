---
description: 
globs: 
alwaysApply: true
---
对于每一个新的策略种类，都需要给出最适合这个策略的回测设置

目前主要是USA地区 UNIVERSE可选有 :  3000 1000 500 200 sp500
NEUTRALIZATION : None,Market,Sector,Industry,subindustry
Decay,Truncation任意设置
比如REGION : USA UNIVERSE : TOP3000 
NEUTRALIZATION : SUBINDUSTRY DECAY : 4 TRUNCATION : 0.08




Arithmetic
abs(x)
base
Absolute value of x
add(x, y, filter = false), x + y
base
Add all inputs (at least 2 inputs required). If filter = true, filter all input NaN to 0 before adding
densify(x)
base
Converts a grouping field of many buckets into lesser number of only available buckets so as to make working with grouping fields computationally efficient
Show more
divide(x, y), x / y
base
x / y
inverse(x)
base
1 / x
log(x)
base
Natural logarithm. For example: Log(high/low) uses natural logarithm of high/low ratio as stock weights.
max(x, y, ..)
base
Maximum value of all inputs. At least 2 inputs are required
Show more
min(x, y ..)
base
Minimum value of all inputs. At least 2 inputs are required
Show more
multiply(x ,y, ... , filter=false), x * y
base
Multiply all inputs. At least 2 inputs are required. Filter sets the NaN values to 1
Show more
power(x, y)
base
x ^ y
Show more
reverse(x)
base
 - x
sign(x)
base
if input = NaN; return NaN
signed_power(x, y)
base
x raised to the power of y such that final result preserves sign of x
Show more
sqrt(x)
base
Square root of x
subtract(x, y, filter=false), x - y
base
x-y. If filter = true, filter all input NaN to 0 before subtracting
Logical
and(input1, input2)
base
Logical AND operator, returns true if both operands are true and returns false otherwise
if_else(input1, input2, input 3)
base
If input1 is true then return input2 else return input3.
Show more
input1 < input2
base
If input1 < input2 return true, else return false
input1 <= input2
base
Returns true if input1 <= input2, return false otherwise
input1 == input2
base
Returns true if both inputs are same and returns false otherwise
input1 > input2
base
Logic comparison operators to compares two inputs
input1 >= input2
base
Returns true if input1 >= input2, return false otherwise
input1!= input2
base
Returns true if both inputs are NOT the same and returns false otherwise
is_nan(input)
base
If (input == NaN) return 1 else return 0
Show more
not(x)
base
Returns the logical negation of x. If x is true (1), it returns false (0), and if input is false (0), it returns true (1).
or(input1, input2)
base
Logical OR operator returns true if either or both inputs are true and returns false otherwise
Time Series
days_from_last_change(x)
base
Amount of days since last change of x
hump(x, hump = 0.01)
base
Limits amount and magnitude of changes in input (thus reducing turnover)
Show more
kth_element(x, d, k)
base
Returns K-th value of input by looking through lookback days. This operator can be used to backfill missing data if k=1
Show more
last_diff_value(x, d)
base
Returns last x value not equal to current x value from last d days
ts_arg_max(x, d)
base
Returns the relative index of the max value in the time series for the past d days. If the current day has the max value for the past d days, it returns 0. If previous day has the max value for the past d days, it returns 1
Show more
ts_arg_min(x, d)
base
Returns the relative index of the min value in the time series for the past d days; If the current day has the min value for the past d days, it returns 0; If previous day has the min value for the past d days, it returns 1.
Show more
ts_av_diff(x, d)
base
Returns x - tsmean(x, d), but deals with NaNs carefully. That is NaNs are ignored during mean computation
Show more
ts_backfill(x,lookback = d, k=1, ignore="NAN")
base
Backfill is the process of replacing the NAN or 0 values by a meaningful value (i.e., a first non-NaN value)
Show more
ts_corr(x, y, d)
base
Returns correlation of x and y for the past d days
Show more
ts_count_nans(x ,d)
base
Returns the number of NaN values in x for the past d days
ts_covariance(y, x, d)
base
Returns covariance of y and x for the past d days
ts_decay_linear(x, d, dense = false)
base
Returns the linear decay on x for the past d days. Dense parameter=false means operator works in sparse mode and we treat NaN as 0. In dense mode we do not.
Show more
ts_delay(x, d)
base
Returns x value d days ago
ts_delta(x, d)
base
Returns x - ts_delay(x, d)
ts_mean(x, d)
base
Returns average value of x for the past d days.
ts_product(x, d)
base
Returns product of x for the past d days
Show more
ts_quantile(x,d, driver="gaussian" )
base
It calculates ts_rank and apply to its value an inverse cumulative density function from driver distribution. Possible values of driver (optional ) are "gaussian", "uniform", "cauchy" distribution where "gaussian" is the default.
ts_rank(x, d, constant = 0)
base
Rank the values of x for each instrument over the past d days, then return the rank of the current value + constant. If not specified, by default, constant = 0.
ts_regression(y, x, d, lag = 0, rettype = 0)
base
Returns various parameters related to regression function
Show more
ts_scale(x, d, constant = 0)
base
Returns (x - ts_min(x, d)) / (ts_max(x, d) - ts_min(x, d)) + constant. This operator is similar to scale down operator but acts in time series space
Show more
ts_std_dev(x, d)
base
Returns standard deviation of x for the past d days
ts_step(1)
base
Returns days' counter
ts_sum(x, d)
base
Sum values of x for the past d days.
ts_zscore(x, d)
base
Z-score is a numerical measurement that describes a value's relationship to the mean of a group of values. Z-score is measured in terms of standard deviations from the mean: (x - tsmean(x,d)) / tsstddev(x,d). This operator may help reduce outliers and drawdown.
Cross Sectional
normalize(x, useStd = false, limit = 0.0)
base
Calculates the mean value of all valid alpha values for a certain date, then subtracts that mean from each element
Show more
quantile(x, driver = gaussian, sigma = 1.0)
base
Rank the raw vector, shift the ranked Alpha vector, apply distribution (gaussian, cauchy, uniform). If driver is uniform, it simply subtract each Alpha value with the mean of all Alpha values in the Alpha vector
Show more
rank(x, rate=2)
base
Ranks the input among all the instruments and returns an equally distributed number between 0.0 and 1.0. For precise sort, use the rate as 0
Show more
scale(x, scale=1, longscale=1, shortscale=1)
base
Scales input to booksize. We can also scale the long positions and short positions to separate scales by mentioning additional parameters to the operator
Show more
winsorize(x, std=4)
base
Winsorizes x to make sure that all values in x are between the lower and upper limits, which are specified as multiple of std.
zscore(x)
base
Z-score is a numerical measurement that describes a value's relationship to the mean of a group of values. Z-score is measured in terms of standard deviations from the mean
Show more
Vector
vec_avg(x)
base
Taking mean of the vector field x
vec_sum(x)
base
Sum of vector field x
Transformational
bucket(rank(x), range="0, 1, 0.1" or buckets = "2,5,6,7,10")
base
Convert float values into indexes for user-specified buckets. Bucket is useful for creating group values, which can be passed to GROUP as input
Show more
trade_when(x, y, z)
base
Used in order to change Alpha values only under a specified condition and to hold Alpha values in other cases. It also allows to close Alpha positions (assign NaN values) under a specified condition
Show more
Group
group_backfill(x, group, d, std = 4.0)
base
If a certain value for a certain date and instrument is NaN, from the set of same group instruments, calculate winsorized mean of all non-NaN values over last d days
Show more
group_mean(x, weight, group)
base
All elements in group equals to the mean
Show more
group_neutralize(x, group)
base
Neutralizes Alpha against groups. These groups can be subindustry, industry, sector, country or a constant
Show more
group_rank(x, group)
base
Each elements in a group is assigned the corresponding rank in this group
Show more
group_scale(x, group)
base
Normalizes the values in a group to be between 0 and 1. (x - groupmin) / (groupmax - groupmin)
group_zscore(x, group)
base
Calculates group Z-score - numerical measurement that describes a value's relationship to the mean of a group of values. Z-score is measured in terms of standard deviations from the mean. zscore = (data - mean) / stddev of x for each instrument within its group.